/**
 * Page Loader Utilities
 *
 * Helper functions to control the page loader manually
 */

/**
 * Show the page loader
 * @param {string} text - Optional text to display
 */
function showPageLoader(text = 'Loading...') {
  if (window.pageLoader) {
    window.pageLoader.show(text);
  }
}

/**
 * Hide the page loader
 */
function hidePageLoader() {
  if (window.pageLoader) {
    window.pageLoader.hide();
  }
}

/**
 * Force hide the page loader (emergency)
 */
function forceHidePageLoader() {
  if (window.pageLoader) {
    window.pageLoader.forceHide();
  }
}

/**
 * Update the loader text while it's showing
 * @param {string} text - New text to display
 */
function updatePageLoaderText(text) {
  if (window.pageLoader) {
    window.pageLoader.updateText(text);
  }
}

/**
 * Check if the page loader is currently active
 * @returns {boolean} - True if loader is showing
 */
function isPageLoaderActive() {
  return window.pageLoader ? window.pageLoader.isLoading() : false;
}

/**
 * Show loader for async operations
 * @param {Promise|Function} operation - Promise or async function to execute
 * @param {string} text - Optional text to display
 * @returns {Promise} - Promise that resolves with the operation result
 */
async function withPageLoader(operation, text = 'Loading...') {
  showPageLoader(text);

  try {
    let result;
    if (typeof operation === 'function') {
      result = await operation();
    } else {
      result = await operation;
    }
    return result;
  } finally {
    hidePageLoader();
  }
}

/**
 * Show loader for form submissions
 * @param {HTMLFormElement} form - The form element
 * @param {string} text - Optional text to display
 */
function showLoaderForForm(form, text = 'Submitting...') {
  if (!form) return;

  showPageLoader(text);

  // Add a class to the form to indicate it's being submitted
  form.classList.add('form-submitting');

  // Disable form elements to prevent double submission
  const elements = form.querySelectorAll('input, button, select, textarea');
  elements.forEach(el => {
    el.setAttribute('data-was-disabled', el.disabled);
    el.disabled = true;
  });
}

/**
 * Hide loader and restore form state
 * @param {HTMLFormElement} form - The form element
 */
function hideLoaderForForm(form) {
  hidePageLoader();

  if (!form) return;

  // Remove the submitting class
  form.classList.remove('form-submitting');

  // Restore form elements
  const elements = form.querySelectorAll('input, button, select, textarea');
  elements.forEach(el => {
    const wasDisabled = el.getAttribute('data-was-disabled') === 'true';
    el.disabled = wasDisabled;
    el.removeAttribute('data-was-disabled');
  });
}

/**
 * Show loader for AJAX requests
 * @param {Promise} request - The AJAX request promise
 * @param {string} text - Optional text to display
 * @returns {Promise} - Promise that resolves with the request result
 */
async function showLoaderForAjax(request, text = 'Loading...') {
  return withPageLoader(request, text);
}

// Make functions globally available
window.showPageLoader = showPageLoader;
window.hidePageLoader = hidePageLoader;
window.forceHidePageLoader = forceHidePageLoader;
window.updatePageLoaderText = updatePageLoaderText;
window.isPageLoaderActive = isPageLoaderActive;
window.withPageLoader = withPageLoader;
window.showLoaderForForm = showLoaderForForm;
window.hideLoaderForForm = hideLoaderForForm;
window.showLoaderForAjax = showLoaderForAjax;

// Export for modules (if needed)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    showPageLoader,
    hidePageLoader,
    forceHidePageLoader,
    updatePageLoaderText,
    isPageLoaderActive,
    withPageLoader,
    showLoaderForForm,
    hideLoaderForForm,
    showLoaderForAjax
  };
}
